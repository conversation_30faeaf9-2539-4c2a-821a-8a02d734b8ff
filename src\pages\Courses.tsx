import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Search, 
  Filter, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Play,
  ChevronDown,
  Grid,
  List
} from 'lucide-react';

interface Course {
  id: string;
  title: string;
  description: string;
  instructor: string;
  duration: string;
  students: number;
  rating: number;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  category: string;
  price: number;
  thumbnail: string;
  lessons: number;
  isEnrolled: boolean;
  progress?: number;
}

const Courses: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedLevel, setSelectedLevel] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popular');

  const categories = ['All', 'Algorithms', 'Data Structures', 'System Design', 'Interview Prep', 'Competitive Programming'];
  const levels = ['All', 'Beginner', 'Intermediate', 'Advanced'];

  const courses: Course[] = [
    {
      id: '1',
      title: 'Complete Data Structures Masterclass',
      description: 'Master all fundamental data structures from arrays to advanced trees and graphs.',
      instructor: 'Alex Chen',
      duration: '12 hours',
      students: 2847,
      rating: 4.9,
      level: 'Intermediate',
      category: 'Data Structures',
      price: 99,
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 45,
      isEnrolled: true,
      progress: 75
    },
    {
      id: '2',
      title: 'Dynamic Programming from Zero to Hero',
      description: 'Learn dynamic programming with step-by-step explanations and real-world examples.',
      instructor: 'Sarah Johnson',
      duration: '8 hours',
      students: 1923,
      rating: 4.8,
      level: 'Advanced',
      category: 'Algorithms',
      price: 79,
      thumbnail: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 32,
      isEnrolled: false
    },
    {
      id: '3',
      title: 'System Design Interview Preparation',
      description: 'Comprehensive guide to system design interviews at top tech companies.',
      instructor: 'Mike Rodriguez',
      duration: '15 hours',
      students: 3421,
      rating: 4.9,
      level: 'Advanced',
      category: 'System Design',
      price: 129,
      thumbnail: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 28,
      isEnrolled: true,
      progress: 20
    },
    {
      id: '4',
      title: 'Algorithms for Beginners',
      description: 'Start your algorithmic journey with sorting, searching, and basic problem-solving.',
      instructor: 'Emily Davis',
      duration: '6 hours',
      students: 4156,
      rating: 4.7,
      level: 'Beginner',
      category: 'Algorithms',
      price: 49,
      thumbnail: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 24,
      isEnrolled: false
    },
    {
      id: '5',
      title: 'Graph Algorithms Deep Dive',
      description: 'Master graph algorithms including BFS, DFS, shortest paths, and minimum spanning trees.',
      instructor: 'David Kim',
      duration: '10 hours',
      students: 1567,
      rating: 4.8,
      level: 'Intermediate',
      category: 'Algorithms',
      price: 89,
      thumbnail: 'https://images.pexels.com/photos/1181316/pexels-photo-1181316.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 38,
      isEnrolled: false
    },
    {
      id: '6',
      title: 'Competitive Programming Bootcamp',
      description: 'Intensive training for competitive programming contests and coding competitions.',
      instructor: 'Lisa Wang',
      duration: '20 hours',
      students: 892,
      rating: 4.9,
      level: 'Advanced',
      category: 'Competitive Programming',
      price: 149,
      thumbnail: 'https://images.pexels.com/photos/1181354/pexels-photo-1181354.jpeg?auto=compress&cs=tinysrgb&w=400',
      lessons: 52,
      isEnrolled: false
    }
  ];

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || course.category === selectedCategory;
    const matchesLevel = selectedLevel === 'All' || course.level === selectedLevel;
    
    return matchesSearch && matchesCategory && matchesLevel;
  });

  const sortedCourses = [...filteredCourses].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.students - a.students;
      case 'rating':
        return b.rating - a.rating;
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return 0; // Would use actual date in real app
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Explore <span className="text-primary">Courses</span>
          </h1>
          <p className="text-gray-400 text-lg">
            Master algorithms and data structures with our comprehensive course library
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search courses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
              >
                {levels.map(level => (
                  <option key={level} value={level}>{level}</option>
                ))}
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
              >
                <option value="popular">Most Popular</option>
                <option value="rating">Highest Rated</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="newest">Newest</option>
              </select>

              {/* View Mode Toggle */}
              <div className="flex bg-dark-tertiary rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-primary text-white' : 'text-gray-400'}`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-primary text-white' : 'text-gray-400'}`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-400">
            Showing {sortedCourses.length} of {courses.length} courses
          </p>
        </div>

        {/* Courses Grid/List */}
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' 
          : 'space-y-6'
        }>
          {sortedCourses.map((course) => (
            <div
              key={course.id}
              className={`bg-dark-secondary rounded-xl overflow-hidden border border-dark-tertiary hover:border-primary/50 transition-all duration-300 hover:-translate-y-1 ${
                viewMode === 'list' ? 'flex' : ''
              }`}
            >
              {/* Thumbnail */}
              <div className={`relative ${viewMode === 'list' ? 'w-64 flex-shrink-0' : ''}`}>
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className={`object-cover ${viewMode === 'list' ? 'w-full h-full' : 'w-full h-48'}`}
                />
                {course.isEnrolled && (
                  <div className="absolute top-3 left-3 bg-primary px-2 py-1 rounded text-xs font-medium">
                    Enrolled
                  </div>
                )}
                <div className="absolute top-3 right-3 bg-black/70 px-2 py-1 rounded text-xs text-white">
                  {course.level}
                </div>
              </div>

              {/* Content */}
              <div className="p-6 flex-1">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-xl font-bold text-white group-hover:text-primary transition-colors">
                    {course.title}
                  </h3>
                  <span className="text-2xl font-bold text-primary">${course.price}</span>
                </div>

                <p className="text-gray-400 mb-4 line-clamp-2">{course.description}</p>

                <div className="flex items-center space-x-4 mb-4 text-sm text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{course.duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{course.lessons} lessons</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{course.students.toLocaleString()}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-white font-medium">{course.rating}</span>
                    </div>
                    <span className="text-gray-400">by {course.instructor}</span>
                  </div>
                </div>

                {course.isEnrolled && course.progress !== undefined && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-1">
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <div className="w-full bg-dark-tertiary rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                <Link
                  to={`/courses/${course.id}`}
                  className={`inline-flex items-center justify-center space-x-2 w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:scale-105 ${
                    course.isEnrolled
                      ? 'bg-primary hover:bg-primary-light text-white'
                      : 'bg-dark-tertiary hover:bg-primary text-gray-300 hover:text-white'
                  }`}
                >
                  {course.isEnrolled ? (
                    <>
                      <Play className="h-4 w-4" />
                      <span>Continue Learning</span>
                    </>
                  ) : (
                    <>
                      <BookOpen className="h-4 w-4" />
                      <span>Enroll Now</span>
                    </>
                  )}
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        {sortedCourses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No courses found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Courses;
