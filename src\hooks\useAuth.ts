import { useUser } from '@stackframe/react';

export const useAuth = () => {
  const user = useUser();

  const isAuthenticated = !!user;
  const userName = user?.displayName || user?.primaryEmail?.split('@')[0] || 'Student';
  const userEmail = user?.primaryEmail || '';
  const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  const joinDate = user?.createdAtMillis 
    ? new Date(user.createdAtMillis).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
    : 'Recently';

  const signOut = () => {
    if (user) {
      user.signOut();
    }
  };

  return {
    user,
    isAuthenticated,
    userName,
    userEmail,
    userInitials,
    joinDate,
    signOut
  };
};
