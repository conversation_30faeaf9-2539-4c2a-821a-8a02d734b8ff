import { useUser } from '@stackframe/react';

export const useAuth = () => {
  try {
    const user = useUser();

    const isAuthenticated = !!user;
    const userName = user?.displayName || user?.primaryEmail?.split('@')[0] || 'Student';
    const userEmail = user?.primaryEmail || '';
    const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    const joinDate = user?.createdAtMillis
      ? new Date(user.createdAtMillis).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
      : 'Recently';

    const signOut = () => {
      try {
        if (user) {
          user.signOut();
        }
      } catch (error) {
        console.error('Error signing out:', error);
      }
    };

    return {
      user,
      isAuthenticated,
      userName,
      userEmail,
      userInitials,
      joinDate,
      signOut
    };
  } catch (error) {
    console.error('Error in useAuth hook:', error);
    return {
      user: null,
      isAuthenticated: false,
      userName: 'Student',
      userEmail: '',
      userInitials: 'ST',
      joinDate: 'Recently',
      signOut: () => {}
    };
  }
};
