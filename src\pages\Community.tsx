import React, { useState } from 'react';
import { useUser } from '@stackframe/react';
import { 
  MessageCircle, 
  Users, 
  TrendingUp, 
  Clock, 
  Heart,
  Reply,
  Share2,
  Bookmark,
  Search,
  Filter,
  Plus,
  Award,
  Star,
  Eye
} from 'lucide-react';

interface Post {
  id: string;
  title: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    reputation: number;
    badge: string;
  };
  category: string;
  tags: string[];
  timestamp: string;
  likes: number;
  replies: number;
  views: number;
  isLiked: boolean;
  isBookmarked: boolean;
  isPinned?: boolean;
}

const Community: React.FC = () => {
  const user = useUser();
  const [activeTab, setActiveTab] = useState<'discussions' | 'questions' | 'showcase'>('discussions');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = ['All', 'General', 'Algorithms', 'Data Structures', 'Interview Prep', 'Career Advice', 'Project Showcase'];

  const posts: Post[] = [
    {
      id: '1',
      title: 'How to approach dynamic programming problems systematically?',
      content: 'I\'ve been struggling with DP problems lately. Can someone share a systematic approach or framework for tackling these types of problems? Any resources or tips would be greatly appreciated!',
      author: {
        name: 'Alex Chen',
        avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
        reputation: 1247,
        badge: 'Expert'
      },
      category: 'Algorithms',
      tags: ['Dynamic Programming', 'Problem Solving', 'Tips'],
      timestamp: '2 hours ago',
      likes: 23,
      replies: 8,
      views: 156,
      isLiked: false,
      isBookmarked: true,
      isPinned: true
    },
    {
      id: '2',
      title: 'Just landed my dream job at Google! 🎉',
      content: 'After 6 months of preparation using AlgoStrive courses, I finally got an offer from Google! The system design course was particularly helpful. Thank you to this amazing community for all the support!',
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
        reputation: 892,
        badge: 'Rising Star'
      },
      category: 'Career Advice',
      tags: ['Success Story', 'Google', 'Interview'],
      timestamp: '4 hours ago',
      likes: 67,
      replies: 15,
      views: 234,
      isLiked: true,
      isBookmarked: false
    },
    {
      id: '3',
      title: 'Built a visualization tool for sorting algorithms',
      content: 'I created an interactive web app that visualizes different sorting algorithms. It shows step-by-step execution with animations. Would love to get feedback from the community!',
      author: {
        name: 'Mike Rodriguez',
        avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
        reputation: 634,
        badge: 'Contributor'
      },
      category: 'Project Showcase',
      tags: ['Visualization', 'Sorting', 'Web Development'],
      timestamp: '6 hours ago',
      likes: 45,
      replies: 12,
      views: 189,
      isLiked: false,
      isBookmarked: true
    },
    {
      id: '4',
      title: 'Best practices for coding interview preparation?',
      content: 'I have interviews coming up at several FAANG companies. What are the most effective strategies for preparation? How much time should I allocate to each topic?',
      author: {
        name: 'Emily Davis',
        avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
        reputation: 456,
        badge: 'Member'
      },
      category: 'Interview Prep',
      tags: ['FAANG', 'Preparation', 'Strategy'],
      timestamp: '8 hours ago',
      likes: 31,
      replies: 19,
      views: 278,
      isLiked: false,
      isBookmarked: false
    },
    {
      id: '5',
      title: 'Understanding time complexity - Big O notation explained',
      content: 'I\'ve put together a comprehensive guide on Big O notation with examples and visual representations. Hope this helps beginners understand time complexity better!',
      author: {
        name: 'David Kim',
        avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
        reputation: 1156,
        badge: 'Expert'
      },
      category: 'Algorithms',
      tags: ['Big O', 'Time Complexity', 'Tutorial'],
      timestamp: '12 hours ago',
      likes: 89,
      replies: 7,
      views: 345,
      isLiked: true,
      isBookmarked: true
    }
  ];

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const communityStats = {
    totalMembers: 15420,
    activeToday: 1247,
    totalPosts: 8934,
    totalAnswers: 23456
  };

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case 'Expert': return 'text-yellow-400 bg-yellow-400/20';
      case 'Rising Star': return 'text-blue-400 bg-blue-400/20';
      case 'Contributor': return 'text-green-400 bg-green-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Community <span className="text-primary">Hub</span>
          </h1>
          <p className="text-gray-400 text-lg">
            Connect with fellow developers, share knowledge, and grow together
          </p>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Members</p>
                <p className="text-2xl font-bold text-white">{communityStats.totalMembers.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Today</p>
                <p className="text-2xl font-bold text-primary">{communityStats.activeToday.toLocaleString()}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Posts</p>
                <p className="text-2xl font-bold text-white">{communityStats.totalPosts.toLocaleString()}</p>
              </div>
              <MessageCircle className="h-8 w-8 text-accent" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Answers</p>
                <p className="text-2xl font-bold text-white">{communityStats.totalAnswers.toLocaleString()}</p>
              </div>
              <Reply className="h-8 w-8 text-accent" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Tabs */}
            <div className="bg-dark-secondary rounded-xl border border-dark-tertiary mb-6">
              <div className="flex border-b border-dark-tertiary">
                {['discussions', 'questions', 'showcase'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab as any)}
                    className={`px-6 py-4 font-medium capitalize transition-colors ${
                      activeTab === tab
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              {/* Search and Filters */}
              <div className="p-6 border-b border-dark-tertiary">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search discussions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
                    />
                  </div>

                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>

                  {user ? (
                    <button className="flex items-center space-x-2 px-6 py-3 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors">
                      <Plus className="h-4 w-4" />
                      <span>New Post</span>
                    </button>
                  ) : (
                    <a
                      href="/handler/signin"
                      className="flex items-center space-x-2 px-6 py-3 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Sign In to Post</span>
                    </a>
                  )}
                </div>
              </div>
            </div>

            {/* Posts List */}
            <div className="space-y-6">
              {filteredPosts.map((post) => (
                <div
                  key={post.id}
                  className={`bg-dark-secondary rounded-xl p-6 border transition-all duration-300 hover:border-primary/50 ${
                    post.isPinned ? 'border-primary/30 bg-primary/5' : 'border-dark-tertiary'
                  }`}
                >
                  {/* Post Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <img 
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="text-white font-semibold">{post.author.name}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getBadgeColor(post.author.badge)}`}>
                            {post.author.badge}
                          </span>
                          <span className="text-gray-400 text-sm">•</span>
                          <span className="text-gray-400 text-sm">{post.timestamp}</span>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-gray-400 text-xs">{post.author.reputation} reputation</span>
                          <span className="text-gray-400 text-xs">•</span>
                          <span className="text-primary text-xs">{post.category}</span>
                        </div>
                      </div>
                    </div>

                    {post.isPinned && (
                      <div className="flex items-center space-x-1 text-primary text-sm">
                        <Award className="h-4 w-4" />
                        <span>Pinned</span>
                      </div>
                    )}
                  </div>

                  {/* Post Content */}
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-white mb-2 hover:text-primary transition-colors cursor-pointer">
                      {post.title}
                    </h3>
                    <p className="text-gray-300 line-clamp-3">{post.content}</p>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm hover:bg-primary/30 transition-colors cursor-pointer"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Post Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <button className={`flex items-center space-x-2 transition-colors ${
                        post.isLiked ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                      }`}>
                        <Heart className={`h-4 w-4 ${post.isLiked ? 'fill-current' : ''}`} />
                        <span className="text-sm">{post.likes}</span>
                      </button>

                      <button className="flex items-center space-x-2 text-gray-400 hover:text-primary transition-colors">
                        <Reply className="h-4 w-4" />
                        <span className="text-sm">{post.replies}</span>
                      </button>

                      <div className="flex items-center space-x-2 text-gray-400">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">{post.views}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button className={`p-2 rounded-lg transition-colors ${
                        post.isBookmarked ? 'text-primary bg-primary/20' : 'text-gray-400 hover:text-primary hover:bg-primary/10'
                      }`}>
                        <Bookmark className={`h-4 w-4 ${post.isBookmarked ? 'fill-current' : ''}`} />
                      </button>

                      <button className="p-2 rounded-lg text-gray-400 hover:text-primary hover:bg-primary/10 transition-colors">
                        <Share2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-8">
              <button className="px-6 py-3 bg-dark-secondary hover:bg-dark-tertiary border border-dark-tertiary rounded-lg font-semibold transition-colors">
                Load More Posts
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Top Contributors */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h3 className="text-xl font-bold text-white mb-4">Top Contributors</h3>
              <div className="space-y-3">
                {[
                  { name: 'Alex Chen', reputation: 1247, badge: 'Expert' },
                  { name: 'Sarah Johnson', reputation: 892, badge: 'Rising Star' },
                  { name: 'Mike Rodriguez', reputation: 634, badge: 'Contributor' }
                ].map((contributor, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium">{contributor.name}</p>
                      <p className="text-gray-400 text-sm">{contributor.reputation} reputation</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getBadgeColor(contributor.badge)}`}>
                      {contributor.badge}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Trending Topics */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h3 className="text-xl font-bold text-white mb-4">Trending Topics</h3>
              <div className="space-y-2">
                {[
                  'Dynamic Programming',
                  'System Design',
                  'FAANG Interviews',
                  'Graph Algorithms',
                  'Binary Trees'
                ].map((topic, index) => (
                  <div key={index} className="flex items-center justify-between p-2 hover:bg-dark-tertiary rounded-lg transition-colors cursor-pointer">
                    <span className="text-gray-300">{topic}</span>
                    <TrendingUp className="h-4 w-4 text-primary" />
                  </div>
                ))}
              </div>
            </div>

            {/* Community Guidelines */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h3 className="text-xl font-bold text-white mb-4">Community Guidelines</h3>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li>• Be respectful and constructive</li>
                <li>• Search before posting</li>
                <li>• Use descriptive titles</li>
                <li>• Include relevant tags</li>
                <li>• Help others learn and grow</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Community;
