import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { St<PERSON><PERSON><PERSON><PERSON>, StackProvider, StackTheme } from "@stackframe/react";
import { stackClientApp } from "./stack";
import Navbar from './components/Navbar';
import ProtectedRoute from './components/ProtectedRoute';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import Hero from './components/Hero';
import VideoShowcase from './components/VideoShowcase';
import TeamSection from './components/TeamSection';
import Footer from './components/Footer';
import AuthPage from './pages/AuthPage';
import Dashboard from './pages/Dashboard';
import Courses from './pages/Courses';
import CourseDetail from './pages/CourseDetail';
import LearningPaths from './pages/LearningPaths';
import Practice from './pages/Practice';
import Community from './pages/Community';
import Pricing from './pages/Pricing';
import Profile from './pages/Profile';

function HandlerRoutes() {
  const location = useLocation();

  return (
    <StackHandler app={stackClientApp} location={location.pathname} fullPage />
  );
}

function AppContent() {
  return (
    <div className="min-h-screen bg-dark text-white">
      <Navbar />
      <Routes>
        <Route path="/" element={
          <main>
            <Hero />
            <VideoShowcase />
            <TeamSection />
          </main>
        } />
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/handler/*" element={<HandlerRoutes />} />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/courses" element={<Courses />} />
        <Route path="/courses/:id" element={<CourseDetail />} />
        <Route path="/learning-paths" element={<LearningPaths />} />
        <Route path="/practice" element={
          <ProtectedRoute>
            <Practice />
          </ProtectedRoute>
        } />
        <Route path="/community" element={<Community />} />
        <Route path="/pricing" element={<Pricing />} />
        <Route path="/profile" element={
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        } />
      </Routes>
      <Footer />
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <div className="min-h-screen bg-dark flex items-center justify-center">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <div className="text-white text-xl">Loading AlgoStrive...</div>
          </div>
        </div>
      }>
        <Router>
          <StackProvider app={stackClientApp}>
            <StackTheme>
              <AppContent />
            </StackTheme>
          </StackProvider>
        </Router>
      </Suspense>
    </ErrorBoundary>
  );
}

export default App;