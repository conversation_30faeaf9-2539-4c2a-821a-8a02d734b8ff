import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users } from 'lucide-react';

const Hero: React.FC = () => {
  const handleWatchVideo = () => {
    const videoSection = document.querySelector('#videos');
    if (videoSection) {
      videoSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark via-dark-secondary to-dark"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-primary/10 via-transparent to-accent/5"></div>
      
      {/* Animated Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(6, 146, 62, 0.3) 1px, transparent 0)',
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-slide-up">
          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            Master{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
              Algorithms
            </span>
            <br />
            & Data Structures
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Join thousands of developers mastering coding interviews and competitive programming 
            through expert-led YouTube content and interactive learning experiences.
          </p>

          {/* Stats */}
          <div className="flex flex-wrap justify-center gap-8 mb-12">
            <div className="flex items-center space-x-2 text-gray-300">
              <Users className="h-5 w-5 text-primary" />
              <span className="font-semibold">50K+</span>
              <span>Students</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-300">
              <Play className="h-5 w-5 text-primary" />
              <span className="font-semibold">200+</span>
              <span>Videos</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-300">
              <Code2 className="h-5 w-5 text-primary" />
              <span className="font-semibold">100+</span>
              <span>Problems</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={handleWatchVideo}
              className="group flex items-center space-x-3 px-8 py-4 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-all duration-200 hover:scale-105 animate-glow"
            >
              <Play className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span>Watch Latest Video</span>
            </button>
            
            <button className="group flex items-center space-x-3 px-8 py-4 border-2 border-primary hover:bg-primary/10 rounded-lg font-semibold transition-all duration-200 hover:scale-105">
              <BookOpen className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span>Browse Curriculum</span>
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>

        {/* Featured Video Preview */}
        <div className="mt-16 animate-fade-in">
          <div className="relative max-w-4xl mx-auto">
            <div className="aspect-video bg-dark-secondary rounded-2xl border border-dark-tertiary overflow-hidden group hover:border-primary/50 transition-colors">
              <div className="w-full h-full bg-gradient-to-br from-dark-secondary to-dark-tertiary flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/30 transition-colors">
                    <Play className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Latest: Binary Search Tree Traversal
                  </h3>
                  <p className="text-gray-400">
                    Learn the fundamentals of BST traversal algorithms
                  </p>
                </div>
              </div>
            </div>
            
            {/* Floating Cards */}
            <div className="absolute -top-8 -left-8 bg-dark-secondary/80 backdrop-blur-lg rounded-lg p-4 border border-dark-tertiary animate-float">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm text-gray-300">Live Tutorial</span>
              </div>
            </div>
            
            <div className="absolute -bottom-8 -right-8 bg-dark-secondary/80 backdrop-blur-lg rounded-lg p-4 border border-dark-tertiary animate-float" style={{ animationDelay: '1s' }}>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="text-sm text-gray-300">Interactive Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;