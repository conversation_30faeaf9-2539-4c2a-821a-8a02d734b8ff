import React, { useState } from 'react';
import { Play, Clock, Eye, ThumbsUp, ExternalLink } from 'lucide-react';

interface Video {
  id: string;
  title: string;
  description: string;
  duration: string;
  views: string;
  likes: string;
  thumbnail: string;
  category: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  youtubeId: string;
}

const VideoShowcase: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);

  const videos: Video[] = [
    {
      id: '1',
      title: 'Binary Search Tree Fundamentals',
      description: 'Master the basics of BST operations including insertion, deletion, and traversal algorithms.',
      duration: '24:31',
      views: '125K',
      likes: '8.2K',
      thumbnail: 'https://images.pexels.com/photos/270348/pexels-photo-270348.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Trees',
      level: 'Beginner',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      id: '2',
      title: 'Dynamic Programming Patterns',
      description: 'Learn the most common DP patterns used in coding interviews with step-by-step explanations.',
      duration: '32:45',
      views: '89K',
      likes: '6.1K',
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Dynamic Programming',
      level: 'Intermediate',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      id: '3',
      title: 'Graph Algorithms Deep Dive',
      description: 'Comprehensive guide to BFS, DFS, and advanced graph algorithms for competitive programming.',
      duration: '41:22',
      views: '167K',
      likes: '12.3K',
      thumbnail: 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Graphs',
      level: 'Advanced',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      id: '4',
      title: 'Array Manipulation Tricks',
      description: 'Essential array techniques every programmer should know for efficient problem solving.',
      duration: '18:56',
      views: '203K',
      likes: '15.7K',
      thumbnail: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Arrays',
      level: 'Beginner',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      id: '5',
      title: 'System Design Basics',
      description: 'Introduction to system design concepts crucial for senior developer interviews.',
      duration: '55:12',
      views: '94K',
      likes: '7.8K',
      thumbnail: 'https://images.pexels.com/photos/1181672/pexels-photo-1181672.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'System Design',
      level: 'Advanced',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      id: '6',
      title: 'Linked List Mastery',
      description: 'Complete guide to linked list operations and advanced pointer manipulation techniques.',
      duration: '27:43',
      views: '156K',
      likes: '11.2K',
      thumbnail: 'https://images.pexels.com/photos/1181673/pexels-photo-1181673.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Linked Lists',
      level: 'Intermediate',
      youtubeId: 'dQw4w9WgXcQ'
    }
  ];

  const categories = ['All', 'Trees', 'Dynamic Programming', 'Graphs', 'Arrays', 'System Design', 'Linked Lists'];

  const filteredVideos = selectedCategory === 'All' 
    ? videos 
    : videos.filter(video => video.category === selectedCategory);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'Intermediate': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'Advanced': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <section id="videos" className="py-20 bg-dark-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Latest <span className="text-primary">Tutorials</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Comprehensive video tutorials covering algorithms, data structures, and coding interview preparation
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                selectedCategory === category
                  ? 'bg-primary text-white shadow-lg'
                  : 'bg-dark-tertiary text-gray-300 hover:bg-dark-tertiary/80 hover:text-white'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredVideos.map((video) => (
            <div
              key={video.id}
              className="group bg-dark-tertiary rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-primary/20 transition-all duration-300 hover:-translate-y-2"
            >
              {/* Thumbnail */}
              <div className="relative overflow-hidden">
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/50 group-hover:bg-black/30 transition-colors"></div>
                
                {/* Play Button */}
                <button
                  onClick={() => setSelectedVideo(video)}
                  className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                >
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center hover:bg-primary-light transition-colors">
                    <Play className="h-8 w-8 text-white ml-1" />
                  </div>
                </button>

                {/* Duration */}
                <div className="absolute bottom-3 right-3 bg-black/80 rounded-lg px-2 py-1 text-sm text-white">
                  {video.duration}
                </div>

                {/* Level Badge */}
                <div className={`absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-medium border ${getLevelColor(video.level)}`}>
                  {video.level}
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-primary transition-colors">
                  {video.title}
                </h3>
                <p className="text-gray-400 mb-4 line-clamp-2">
                  {video.description}
                </p>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>{video.views}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{video.likes}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{video.duration}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="inline-flex items-center space-x-2 px-8 py-4 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-all duration-200 hover:scale-105">
            <span>Load More Videos</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Video Modal */}
      {selectedVideo && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-dark-secondary rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-2xl font-semibold text-white">{selectedVideo.title}</h3>
                <button
                  onClick={() => setSelectedVideo(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="aspect-video bg-dark-tertiary rounded-lg mb-4 flex items-center justify-center">
                <div className="text-center">
                  <Play className="h-16 w-16 text-primary mx-auto mb-4" />
                  <p className="text-gray-400">Video player would be embedded here</p>
                  <p className="text-sm text-gray-500 mt-2">YouTube ID: {selectedVideo.youtubeId}</p>
                </div>
              </div>
              
              <p className="text-gray-300">{selectedVideo.description}</p>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default VideoShowcase;