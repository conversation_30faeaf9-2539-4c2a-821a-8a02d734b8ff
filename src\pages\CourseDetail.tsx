import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  CheckCircle, 
  Lock,
  Download,
  Share2,
  Heart,
  ArrowLeft,
  Trophy,
  Target
} from 'lucide-react';

interface Lesson {
  id: string;
  title: string;
  duration: string;
  isCompleted: boolean;
  isLocked: boolean;
  type: 'video' | 'quiz' | 'exercise';
}

interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
  isCompleted: boolean;
}

const CourseDetail: React.FC = () => {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState<'overview' | 'curriculum' | 'reviews'>('overview');
  const [isEnrolled, setIsEnrolled] = useState(true);

  // Mock course data - in real app, this would come from API
  const course = {
    id: '1',
    title: 'Complete Data Structures Masterclass',
    description: 'Master all fundamental data structures from arrays to advanced trees and graphs. This comprehensive course covers everything you need to know about data structures for coding interviews and real-world applications.',
    instructor: {
      name: '<PERSON>',
      title: 'Senior Software Engineer at Google',
      avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Former Google SWE with 8+ years in competitive programming. Passionate about making complex algorithms accessible to everyone.'
    },
    duration: '12 hours',
    students: 2847,
    rating: 4.9,
    reviews: 342,
    level: 'Intermediate',
    category: 'Data Structures',
    price: 99,
    thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400',
    totalLessons: 45,
    completedLessons: 34,
    progress: 75,
    skills: ['Arrays', 'Linked Lists', 'Trees', 'Graphs', 'Hash Tables', 'Heaps'],
    requirements: ['Basic programming knowledge', 'Understanding of loops and functions', 'Any programming language'],
    whatYouLearn: [
      'Master all fundamental data structures',
      'Implement data structures from scratch',
      'Solve complex algorithmic problems',
      'Optimize time and space complexity',
      'Prepare for technical interviews'
    ]
  };

  const modules: Module[] = [
    {
      id: '1',
      title: 'Introduction to Data Structures',
      isCompleted: true,
      lessons: [
        { id: '1-1', title: 'What are Data Structures?', duration: '15 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '1-2', title: 'Time and Space Complexity', duration: '20 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '1-3', title: 'Big O Notation Quiz', duration: '10 min', isCompleted: true, isLocked: false, type: 'quiz' }
      ]
    },
    {
      id: '2',
      title: 'Arrays and Strings',
      isCompleted: true,
      lessons: [
        { id: '2-1', title: 'Array Fundamentals', duration: '25 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '2-2', title: 'Dynamic Arrays', duration: '18 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '2-3', title: 'String Manipulation', duration: '22 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '2-4', title: 'Array Problems Practice', duration: '30 min', isCompleted: true, isLocked: false, type: 'exercise' }
      ]
    },
    {
      id: '3',
      title: 'Linked Lists',
      isCompleted: false,
      lessons: [
        { id: '3-1', title: 'Singly Linked Lists', duration: '28 min', isCompleted: true, isLocked: false, type: 'video' },
        { id: '3-2', title: 'Doubly Linked Lists', duration: '24 min', isCompleted: false, isLocked: false, type: 'video' },
        { id: '3-3', title: 'Circular Linked Lists', duration: '20 min', isCompleted: false, isLocked: false, type: 'video' },
        { id: '3-4', title: 'Linked List Algorithms', duration: '35 min', isCompleted: false, isLocked: false, type: 'exercise' }
      ]
    },
    {
      id: '4',
      title: 'Trees and Binary Search Trees',
      isCompleted: false,
      lessons: [
        { id: '4-1', title: 'Tree Fundamentals', duration: '30 min', isCompleted: false, isLocked: true, type: 'video' },
        { id: '4-2', title: 'Binary Trees', duration: '25 min', isCompleted: false, isLocked: true, type: 'video' },
        { id: '4-3', title: 'Binary Search Trees', duration: '32 min', isCompleted: false, isLocked: true, type: 'video' },
        { id: '4-4', title: 'Tree Traversal Algorithms', duration: '28 min', isCompleted: false, isLocked: true, type: 'video' }
      ]
    }
  ];

  const reviews = [
    {
      id: '1',
      user: 'Sarah M.',
      rating: 5,
      comment: 'Excellent course! Alex explains complex concepts in a very clear and understandable way.',
      date: '2 weeks ago'
    },
    {
      id: '2',
      user: 'Mike R.',
      rating: 5,
      comment: 'This course helped me land my dream job at a FAANG company. Highly recommended!',
      date: '1 month ago'
    },
    {
      id: '3',
      user: 'Emily K.',
      rating: 4,
      comment: 'Great content and examples. The practice problems are very helpful.',
      date: '3 weeks ago'
    }
  ];

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <Link 
          to="/courses"
          className="inline-flex items-center space-x-2 text-gray-400 hover:text-primary transition-colors mb-6"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Courses</span>
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Header */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary mb-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">{course.title}</h1>
                  <p className="text-gray-400 mb-4">{course.description}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{course.duration}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4" />
                      <span>{course.totalLessons} lessons</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{course.students.toLocaleString()} students</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span>{course.rating} ({course.reviews} reviews)</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button className="p-2 bg-dark-tertiary hover:bg-primary/20 rounded-lg transition-colors">
                    <Heart className="h-5 w-5 text-gray-400" />
                  </button>
                  <button className="p-2 bg-dark-tertiary hover:bg-primary/20 rounded-lg transition-colors">
                    <Share2 className="h-5 w-5 text-gray-400" />
                  </button>
                </div>
              </div>

              {/* Progress Bar (if enrolled) */}
              {isEnrolled && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
                    <span>Your Progress</span>
                    <span>{course.completedLessons}/{course.totalLessons} lessons completed</span>
                  </div>
                  <div className="w-full bg-dark-tertiary rounded-full h-3">
                    <div 
                      className="bg-primary h-3 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Skills */}
              <div className="mb-4">
                <h3 className="text-white font-semibold mb-2">Skills you'll gain:</h3>
                <div className="flex flex-wrap gap-2">
                  {course.skills.map((skill, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="bg-dark-secondary rounded-xl border border-dark-tertiary">
              <div className="flex border-b border-dark-tertiary">
                {['overview', 'curriculum', 'reviews'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab as any)}
                    className={`px-6 py-4 font-medium capitalize transition-colors ${
                      activeTab === tab
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              <div className="p-6">
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">What you'll learn</h3>
                      <ul className="space-y-2">
                        {course.whatYouLearn.map((item, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <CheckCircle className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-gray-300">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">Requirements</h3>
                      <ul className="space-y-2">
                        {course.requirements.map((req, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-300">{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">Instructor</h3>
                      <div className="flex items-start space-x-4">
                        <img 
                          src={course.instructor.avatar}
                          alt={course.instructor.name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                        <div>
                          <h4 className="text-white font-semibold">{course.instructor.name}</h4>
                          <p className="text-gray-400 text-sm mb-2">{course.instructor.title}</p>
                          <p className="text-gray-300">{course.instructor.bio}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'curriculum' && (
                  <div className="space-y-4">
                    {modules.map((module) => (
                      <div key={module.id} className="border border-dark-tertiary rounded-lg">
                        <div className="flex items-center justify-between p-4 bg-dark-tertiary rounded-t-lg">
                          <div className="flex items-center space-x-3">
                            {module.isCompleted ? (
                              <CheckCircle className="h-5 w-5 text-primary" />
                            ) : (
                              <div className="w-5 h-5 border-2 border-gray-400 rounded-full"></div>
                            )}
                            <h3 className="text-white font-semibold">{module.title}</h3>
                          </div>
                          <span className="text-gray-400 text-sm">
                            {module.lessons.length} lessons
                          </span>
                        </div>
                        <div className="p-4 space-y-2">
                          {module.lessons.map((lesson) => (
                            <div key={lesson.id} className="flex items-center justify-between p-3 hover:bg-dark-tertiary rounded-lg transition-colors">
                              <div className="flex items-center space-x-3">
                                {lesson.isLocked ? (
                                  <Lock className="h-4 w-4 text-gray-500" />
                                ) : lesson.isCompleted ? (
                                  <CheckCircle className="h-4 w-4 text-primary" />
                                ) : (
                                  <Play className="h-4 w-4 text-gray-400" />
                                )}
                                <span className={`${lesson.isLocked ? 'text-gray-500' : 'text-white'}`}>
                                  {lesson.title}
                                </span>
                                <span className="text-xs px-2 py-1 bg-dark-tertiary rounded text-gray-400">
                                  {lesson.type}
                                </span>
                              </div>
                              <span className="text-gray-400 text-sm">{lesson.duration}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-white">Student Reviews</h3>
                      <div className="flex items-center space-x-2">
                        <Star className="h-5 w-5 text-yellow-400 fill-current" />
                        <span className="text-white font-semibold">{course.rating}</span>
                        <span className="text-gray-400">({course.reviews} reviews)</span>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      {reviews.map((review) => (
                        <div key={review.id} className="p-4 bg-dark-tertiary rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-white font-medium">{review.user}</span>
                              <div className="flex">
                                {[...Array(review.rating)].map((_, i) => (
                                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                                ))}
                              </div>
                            </div>
                            <span className="text-gray-400 text-sm">{review.date}</span>
                          </div>
                          <p className="text-gray-300">{review.comment}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Course Card */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary sticky top-24">
              <img 
                src={course.thumbnail}
                alt={course.title}
                className="w-full h-48 object-cover rounded-lg mb-4"
              />
              
              <div className="text-center mb-6">
                <span className="text-3xl font-bold text-primary">${course.price}</span>
              </div>

              {isEnrolled ? (
                <div className="space-y-3">
                  <button className="w-full py-3 px-4 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2">
                    <Play className="h-4 w-4" />
                    <span>Continue Learning</span>
                  </button>
                  <button className="w-full py-3 px-4 bg-dark-tertiary hover:bg-dark-tertiary/80 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2">
                    <Download className="h-4 w-4" />
                    <span>Download Resources</span>
                  </button>
                </div>
              ) : (
                <button className="w-full py-3 px-4 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors">
                  Enroll Now
                </button>
              )}

              <div className="mt-6 space-y-3 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Level:</span>
                  <span className="text-white">{course.level}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Duration:</span>
                  <span className="text-white">{course.duration}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Students:</span>
                  <span className="text-white">{course.students.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Certificate:</span>
                  <span className="text-white">Yes</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
