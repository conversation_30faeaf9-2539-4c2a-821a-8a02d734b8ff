import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { 
  BookOpen, 
  Clock, 
  Trophy, 
  Target, 
  TrendingUp, 
  Play, 
  CheckCircle, 
  Star,
  Calendar,
  Users,
  Code,
  Award
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { userName } = useAuth();
  const recentCourses = [
    {
      id: '1',
      title: 'Advanced Data Structures',
      progress: 75,
      nextLesson: 'Binary Search Trees',
      timeLeft: '2h 30m',
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: '2',
      title: 'Dynamic Programming Mastery',
      progress: 45,
      nextLesson: 'Knapsack Problem',
      timeLeft: '4h 15m',
      thumbnail: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: '3',
      title: 'System Design Fundamentals',
      progress: 20,
      nextLesson: 'Load Balancing',
      timeLeft: '8h 45m',
      thumbnail: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ];

  const achievements = [
    { icon: Trophy, title: 'Problem Solver', description: 'Solved 100+ problems', earned: true },
    { icon: Target, title: 'Streak Master', description: '30-day learning streak', earned: true },
    { icon: Star, title: 'Course Completer', description: 'Completed 5 courses', earned: false },
    { icon: Award, title: 'Top Performer', description: 'Top 10% in challenges', earned: false }
  ];

  const upcomingEvents = [
    { title: 'Live Coding Session', time: 'Today, 3:00 PM', type: 'live' },
    { title: 'Algorithm Challenge', time: 'Tomorrow, 10:00 AM', type: 'challenge' },
    { title: 'Study Group Meeting', time: 'Friday, 7:00 PM', type: 'community' }
  ];

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome back, <span className="text-primary">{userName}</span>! 👋
          </h1>
          <p className="text-gray-400 text-lg">
            Ready to continue your coding journey? Let's pick up where you left off.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Courses Enrolled</p>
                <p className="text-2xl font-bold text-white">12</p>
              </div>
              <BookOpen className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Hours Learned</p>
                <p className="text-2xl font-bold text-white">47</p>
              </div>
              <Clock className="h-8 w-8 text-accent" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Problems Solved</p>
                <p className="text-2xl font-bold text-white">156</p>
              </div>
              <Code className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Current Streak</p>
                <p className="text-2xl font-bold text-white">23 days</p>
              </div>
              <TrendingUp className="h-8 w-8 text-accent" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Continue Learning */}
          <div className="lg:col-span-2">
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h2 className="text-2xl font-bold text-white mb-6">Continue Learning</h2>
              <div className="space-y-4">
                {recentCourses.map((course) => (
                  <div key={course.id} className="bg-dark-tertiary rounded-lg p-4 hover:bg-dark-tertiary/80 transition-colors">
                    <div className="flex items-center space-x-4">
                      <img 
                        src={course.thumbnail} 
                        alt={course.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h3 className="text-white font-semibold">{course.title}</h3>
                        <p className="text-gray-400 text-sm">Next: {course.nextLesson}</p>
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-sm text-gray-400 mb-1">
                            <span>Progress</span>
                            <span>{course.progress}%</span>
                          </div>
                          <div className="w-full bg-dark rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full transition-all duration-300"
                              style={{ width: `${course.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-gray-400 text-sm">{course.timeLeft} left</p>
                        <Link 
                          to={`/courses/${course.id}`}
                          className="inline-flex items-center space-x-1 mt-2 px-3 py-1 bg-primary hover:bg-primary-light rounded-lg text-sm font-medium transition-colors"
                        >
                          <Play className="h-3 w-3" />
                          <span>Continue</span>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Achievements */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary mt-6">
              <h2 className="text-2xl font-bold text-white mb-6">Achievements</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {achievements.map((achievement, index) => (
                  <div 
                    key={index}
                    className={`p-4 rounded-lg border ${
                      achievement.earned 
                        ? 'bg-primary/10 border-primary/30' 
                        : 'bg-dark-tertiary border-dark-tertiary'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <achievement.icon 
                        className={`h-8 w-8 ${
                          achievement.earned ? 'text-primary' : 'text-gray-500'
                        }`} 
                      />
                      <div>
                        <h3 className={`font-semibold ${
                          achievement.earned ? 'text-white' : 'text-gray-400'
                        }`}>
                          {achievement.title}
                        </h3>
                        <p className="text-gray-400 text-sm">{achievement.description}</p>
                      </div>
                      {achievement.earned && (
                        <CheckCircle className="h-5 w-5 text-primary ml-auto" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Events */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h3 className="text-xl font-bold text-white mb-4">Upcoming Events</h3>
              <div className="space-y-3">
                {upcomingEvents.map((event, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-dark-tertiary rounded-lg">
                    <Calendar className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-white font-medium text-sm">{event.title}</p>
                      <p className="text-gray-400 text-xs">{event.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
              <h3 className="text-xl font-bold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link 
                  to="/practice"
                  className="flex items-center space-x-3 p-3 bg-dark-tertiary hover:bg-primary/10 rounded-lg transition-colors group"
                >
                  <Code className="h-5 w-5 text-primary" />
                  <span className="text-white group-hover:text-primary">Practice Problems</span>
                </Link>
                <Link 
                  to="/community"
                  className="flex items-center space-x-3 p-3 bg-dark-tertiary hover:bg-primary/10 rounded-lg transition-colors group"
                >
                  <Users className="h-5 w-5 text-primary" />
                  <span className="text-white group-hover:text-primary">Join Discussion</span>
                </Link>
                <Link 
                  to="/courses"
                  className="flex items-center space-x-3 p-3 bg-dark-tertiary hover:bg-primary/10 rounded-lg transition-colors group"
                >
                  <BookOpen className="h-5 w-5 text-primary" />
                  <span className="text-white group-hover:text-primary">Browse Courses</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
