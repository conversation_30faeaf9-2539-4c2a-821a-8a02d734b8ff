import React, { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useUser } from '@stackframe/react';
import { 
  User, 
  Mail, 
  Calendar, 
  MapPin, 
  Edit3, 
  Save,
  Camera,
  Trophy,
  Target,
  Clock,
  BookOpen,
  Code,
  Award,
  Star,
  TrendingUp,
  Settings,
  Bell,
  Shield,
  CreditCard
} from 'lucide-react';

const Profile: React.FC = () => {
  const user = useUser();
  const [activeTab, setActiveTab] = useState<'profile' | 'achievements' | 'settings'>('profile');
  const [isEditing, setIsEditing] = useState(false);

  // Redirect to sign in if not authenticated
  if (!user) {
    return <Navigate to="/handler/signin" replace />;
  }

  const [profileData, setProfileData] = useState({
    name: user.displayName || user.primaryEmail?.split('@')[0] || 'Student',
    email: user.primaryEmail || '',
    bio: 'Passionate software engineer with 5+ years of experience. Love solving complex algorithmic problems and helping others learn.',
    location: 'San Francisco, CA',
    joinDate: new Date(user.createdAtMillis).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
    website: '',
    github: '',
    linkedin: ''
  });

  const stats = {
    coursesCompleted: 12,
    problemsSolved: 156,
    hoursLearned: 47,
    currentStreak: 23,
    totalPoints: 2847,
    rank: 'Expert'
  };

  const achievements = [
    { 
      id: '1', 
      title: 'Problem Solver', 
      description: 'Solved 100+ problems', 
      icon: Trophy, 
      earned: true, 
      earnedDate: '2024-01-15',
      rarity: 'Common'
    },
    { 
      id: '2', 
      title: 'Streak Master', 
      description: '30-day learning streak', 
      icon: Target, 
      earned: true, 
      earnedDate: '2024-02-20',
      rarity: 'Rare'
    },
    { 
      id: '3', 
      title: 'Course Completer', 
      description: 'Completed 10 courses', 
      icon: BookOpen, 
      earned: true, 
      earnedDate: '2024-03-10',
      rarity: 'Common'
    },
    { 
      id: '4', 
      title: 'Speed Demon', 
      description: 'Solved problem in under 5 minutes', 
      icon: Clock, 
      earned: true, 
      earnedDate: '2024-02-05',
      rarity: 'Epic'
    },
    { 
      id: '5', 
      title: 'Algorithm Master', 
      description: 'Master all algorithm categories', 
      icon: Code, 
      earned: false, 
      earnedDate: null,
      rarity: 'Legendary'
    },
    { 
      id: '6', 
      title: 'Top Performer', 
      description: 'Top 10% in monthly challenge', 
      icon: Award, 
      earned: false, 
      earnedDate: null,
      rarity: 'Epic'
    }
  ];

  const recentActivity = [
    { type: 'course', title: 'Completed "Advanced Data Structures"', date: '2 hours ago' },
    { type: 'problem', title: 'Solved "Binary Tree Maximum Path Sum"', date: '5 hours ago' },
    { type: 'achievement', title: 'Earned "Streak Master" achievement', date: '1 day ago' },
    { type: 'course', title: 'Started "System Design Fundamentals"', date: '2 days ago' },
    { type: 'problem', title: 'Solved "Longest Palindromic Substring"', date: '3 days ago' }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'text-gray-400 bg-gray-400/20';
      case 'Rare': return 'text-blue-400 bg-blue-400/20';
      case 'Epic': return 'text-purple-400 bg-purple-400/20';
      case 'Legendary': return 'text-yellow-400 bg-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  const handleSave = () => {
    setIsEditing(false);
    // Save profile data logic here
  };

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="bg-dark-secondary rounded-2xl p-8 border border-dark-tertiary mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <div className="w-32 h-32 bg-primary rounded-full flex items-center justify-center text-4xl font-bold text-white">
                {profileData.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
              </div>
              <button className="absolute bottom-0 right-0 p-2 bg-primary rounded-full hover:bg-primary-light transition-colors">
                <Camera className="h-4 w-4 text-white" />
              </button>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-white">{profileData.name}</h1>
                  <p className="text-gray-400">{profileData.email}</p>
                </div>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-light rounded-lg transition-colors"
                >
                  <Edit3 className="h-4 w-4" />
                  <span>{isEditing ? 'Cancel' : 'Edit Profile'}</span>
                </button>
              </div>

              <p className="text-gray-300 mb-4">{profileData.bio}</p>

              <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{profileData.location}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>Joined {profileData.joinDate}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Trophy className="h-4 w-4" />
                  <span>{stats.rank} Level</span>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{stats.coursesCompleted}</p>
                <p className="text-gray-400 text-sm">Courses</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{stats.problemsSolved}</p>
                <p className="text-gray-400 text-sm">Problems</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{stats.hoursLearned}</p>
                <p className="text-gray-400 text-sm">Hours</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{stats.currentStreak}</p>
                <p className="text-gray-400 text-sm">Day Streak</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-dark-secondary rounded-xl border border-dark-tertiary mb-8">
          <div className="flex border-b border-dark-tertiary">
            {[
              { id: 'profile', label: 'Profile', icon: User },
              { id: 'achievements', label: 'Achievements', icon: Trophy },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-6 py-4 font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>

          <div className="p-6">
            {activeTab === 'profile' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Profile Form */}
                <div className="lg:col-span-2">
                  {isEditing ? (
                    <div className="space-y-6">
                      <div>
                        <label className="block text-white font-medium mb-2">Full Name</label>
                        <input
                          type="text"
                          value={profileData.name}
                          onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                          className="w-full px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-medium mb-2">Bio</label>
                        <textarea
                          value={profileData.bio}
                          onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                          rows={4}
                          className="w-full px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-medium mb-2">Location</label>
                        <input
                          type="text"
                          value={profileData.location}
                          onChange={(e) => setProfileData({...profileData, location: e.target.value})}
                          className="w-full px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
                        />
                      </div>
                      <button
                        onClick={handleSave}
                        className="flex items-center space-x-2 px-6 py-3 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors"
                      >
                        <Save className="h-4 w-4" />
                        <span>Save Changes</span>
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-4">Recent Activity</h3>
                        <div className="space-y-3">
                          {recentActivity.map((activity, index) => (
                            <div key={index} className="flex items-center space-x-3 p-3 bg-dark-tertiary rounded-lg">
                              <div className={`w-2 h-2 rounded-full ${
                                activity.type === 'course' ? 'bg-primary' :
                                activity.type === 'problem' ? 'bg-accent' : 'bg-yellow-400'
                              }`}></div>
                              <div className="flex-1">
                                <p className="text-white">{activity.title}</p>
                                <p className="text-gray-400 text-sm">{activity.date}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Progress Overview */}
                <div className="space-y-6">
                  <div className="bg-dark-tertiary rounded-xl p-6">
                    <h3 className="text-xl font-bold text-white mb-4">Learning Progress</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-400">Overall Progress</span>
                          <span className="text-white">75%</span>
                        </div>
                        <div className="w-full bg-dark rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full" style={{ width: '75%' }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-400">Algorithms</span>
                          <span className="text-white">85%</span>
                        </div>
                        <div className="w-full bg-dark rounded-full h-2">
                          <div className="bg-accent h-2 rounded-full" style={{ width: '85%' }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-400">Data Structures</span>
                          <span className="text-white">70%</span>
                        </div>
                        <div className="w-full bg-dark rounded-full h-2">
                          <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '70%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-dark-tertiary rounded-xl p-6">
                    <h3 className="text-xl font-bold text-white mb-4">Rank & Points</h3>
                    <div className="text-center">
                      <div className="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Trophy className="h-10 w-10 text-primary" />
                      </div>
                      <p className="text-2xl font-bold text-white">{stats.rank}</p>
                      <p className="text-gray-400">{stats.totalPoints} points</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'achievements' && (
              <div>
                <h3 className="text-2xl font-bold text-white mb-6">Achievements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {achievements.map((achievement) => (
                    <div
                      key={achievement.id}
                      className={`p-6 rounded-xl border transition-all duration-300 ${
                        achievement.earned
                          ? 'bg-primary/10 border-primary/30 hover:border-primary/50'
                          : 'bg-dark-tertiary border-dark-tertiary hover:border-gray-600'
                      }`}
                    >
                      <div className="flex items-center space-x-4 mb-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          achievement.earned ? 'bg-primary/20' : 'bg-gray-600/20'
                        }`}>
                          <achievement.icon className={`h-6 w-6 ${
                            achievement.earned ? 'text-primary' : 'text-gray-500'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <h4 className={`font-bold ${achievement.earned ? 'text-white' : 'text-gray-400'}`}>
                            {achievement.title}
                          </h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(achievement.rarity)}`}>
                            {achievement.rarity}
                          </span>
                        </div>
                      </div>
                      <p className={`text-sm ${achievement.earned ? 'text-gray-300' : 'text-gray-500'}`}>
                        {achievement.description}
                      </p>
                      {achievement.earned && achievement.earnedDate && (
                        <p className="text-xs text-gray-400 mt-2">
                          Earned on {new Date(achievement.earnedDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-6">Account Settings</h3>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 bg-dark-tertiary rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Bell className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-white font-medium">Email Notifications</p>
                          <p className="text-gray-400 text-sm">Receive updates about your progress</p>
                        </div>
                      </div>
                      <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary">
                        <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6 transition-transform" />
                      </button>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-dark-tertiary rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Shield className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-white font-medium">Two-Factor Authentication</p>
                          <p className="text-gray-400 text-sm">Add an extra layer of security</p>
                        </div>
                      </div>
                      <button className="px-4 py-2 bg-primary hover:bg-primary-light rounded-lg text-white font-medium transition-colors">
                        Enable
                      </button>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-dark-tertiary rounded-lg">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-white font-medium">Billing & Subscription</p>
                          <p className="text-gray-400 text-sm">Manage your subscription plan</p>
                        </div>
                      </div>
                      <button className="px-4 py-2 bg-dark-secondary hover:bg-dark-tertiary border border-dark-tertiary rounded-lg text-white font-medium transition-colors">
                        Manage
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-white mb-4">Danger Zone</h3>
                  <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                    <p className="text-white font-medium mb-2">Delete Account</p>
                    <p className="text-gray-400 text-sm mb-4">
                      Once you delete your account, there is no going back. Please be certain.
                    </p>
                    <button className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white font-medium transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
