import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { 
  Code, 
  Clock, 
  Trophy, 
  Target, 
  Filter,
  Search,
  Play,
  CheckCircle,
  Star,
  Zap,
  Brain,
  Award
} from 'lucide-react';

interface Problem {
  id: string;
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  tags: string[];
  timeLimit: string;
  acceptanceRate: number;
  solved: boolean;
  attempts: number;
  likes: number;
  companies: string[];
}

const Practice: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showSolvedOnly, setShowSolvedOnly] = useState(false);

  const categories = ['All', 'Arrays', 'Strings', 'Trees', 'Graphs', 'Dynamic Programming', 'Sorting', 'Searching'];
  const difficulties = ['All', 'Easy', 'Medium', 'Hard'];

  const problems: Problem[] = [
    {
      id: '1',
      title: 'Two Sum',
      description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
      difficulty: 'Easy',
      category: 'Arrays',
      tags: ['Array', 'Hash Table'],
      timeLimit: '30 min',
      acceptanceRate: 85,
      solved: true,
      attempts: 2,
      likes: 1247,
      companies: ['Google', 'Amazon', 'Microsoft']
    },
    {
      id: '2',
      title: 'Longest Substring Without Repeating Characters',
      description: 'Given a string s, find the length of the longest substring without repeating characters.',
      difficulty: 'Medium',
      category: 'Strings',
      tags: ['String', 'Sliding Window', 'Hash Table'],
      timeLimit: '45 min',
      acceptanceRate: 67,
      solved: true,
      attempts: 3,
      likes: 892,
      companies: ['Facebook', 'Apple', 'Netflix']
    },
    {
      id: '3',
      title: 'Binary Tree Inorder Traversal',
      description: 'Given the root of a binary tree, return the inorder traversal of its nodes\' values.',
      difficulty: 'Easy',
      category: 'Trees',
      tags: ['Tree', 'DFS', 'Binary Tree'],
      timeLimit: '25 min',
      acceptanceRate: 78,
      solved: false,
      attempts: 1,
      likes: 634,
      companies: ['Google', 'Microsoft', 'Adobe']
    },
    {
      id: '4',
      title: 'Maximum Subarray',
      description: 'Given an integer array nums, find the contiguous subarray which has the largest sum and return its sum.',
      difficulty: 'Medium',
      category: 'Dynamic Programming',
      tags: ['Array', 'Dynamic Programming', 'Divide and Conquer'],
      timeLimit: '40 min',
      acceptanceRate: 72,
      solved: false,
      attempts: 0,
      likes: 1156,
      companies: ['Amazon', 'Bloomberg', 'LinkedIn']
    },
    {
      id: '5',
      title: 'Merge k Sorted Lists',
      description: 'You are given an array of k linked-lists lists, each linked-list is sorted in ascending order.',
      difficulty: 'Hard',
      category: 'Trees',
      tags: ['Linked List', 'Divide and Conquer', 'Heap', 'Merge Sort'],
      timeLimit: '60 min',
      acceptanceRate: 45,
      solved: false,
      attempts: 0,
      likes: 987,
      companies: ['Google', 'Facebook', 'Uber']
    },
    {
      id: '6',
      title: 'Valid Parentheses',
      description: 'Given a string s containing just the characters \'(\', \')\', \'{\', \'}\', \'[\' and \']\', determine if the input string is valid.',
      difficulty: 'Easy',
      category: 'Strings',
      tags: ['String', 'Stack'],
      timeLimit: '20 min',
      acceptanceRate: 91,
      solved: true,
      attempts: 1,
      likes: 743,
      companies: ['Microsoft', 'Amazon', 'Apple']
    }
  ];

  const filteredProblems = problems.filter(problem => {
    const matchesSearch = problem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         problem.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDifficulty = selectedDifficulty === 'All' || problem.difficulty === selectedDifficulty;
    const matchesCategory = selectedCategory === 'All' || problem.category === selectedCategory;
    const matchesSolved = !showSolvedOnly || problem.solved;
    
    return matchesSearch && matchesDifficulty && matchesCategory && matchesSolved;
  });

  const stats = {
    totalSolved: problems.filter(p => p.solved).length,
    totalProblems: problems.length,
    easyCompleted: problems.filter(p => p.solved && p.difficulty === 'Easy').length,
    mediumCompleted: problems.filter(p => p.solved && p.difficulty === 'Medium').length,
    hardCompleted: problems.filter(p => p.solved && p.difficulty === 'Hard').length,
    currentStreak: 7
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Hard': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyBg = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-400/20 border-green-400/30';
      case 'Medium': return 'bg-yellow-400/20 border-yellow-400/30';
      case 'Hard': return 'bg-red-400/20 border-red-400/30';
      default: return 'bg-gray-400/20 border-gray-400/30';
    }
  };

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Practice <span className="text-primary">Problems</span>
          </h1>
          <p className="text-gray-400 text-lg">
            Sharpen your coding skills with our curated collection of algorithmic challenges
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Problems Solved</p>
                <p className="text-2xl font-bold text-white">{stats.totalSolved}/{stats.totalProblems}</p>
              </div>
              <Trophy className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Easy</p>
                <p className="text-2xl font-bold text-green-400">{stats.easyCompleted}</p>
              </div>
              <Target className="h-8 w-8 text-green-400" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Medium</p>
                <p className="text-2xl font-bold text-yellow-400">{stats.mediumCompleted}</p>
              </div>
              <Brain className="h-8 w-8 text-yellow-400" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Hard</p>
                <p className="text-2xl font-bold text-red-400">{stats.hardCompleted}</p>
              </div>
              <Zap className="h-8 w-8 text-red-400" />
            </div>
          </div>
          
          <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Current Streak</p>
                <p className="text-2xl font-bold text-primary">{stats.currentStreak} days</p>
              </div>
              <Award className="h-8 w-8 text-primary" />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search problems..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
              >
                {difficulties.map(difficulty => (
                  <option key={difficulty} value={difficulty}>{difficulty}</option>
                ))}
              </select>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg text-white focus:outline-none focus:border-primary"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <label className="flex items-center space-x-2 px-4 py-3 bg-dark-tertiary border border-dark-tertiary rounded-lg cursor-pointer">
                <input
                  type="checkbox"
                  checked={showSolvedOnly}
                  onChange={(e) => setShowSolvedOnly(e.target.checked)}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-white text-sm">Solved Only</span>
              </label>
            </div>
          </div>
        </div>

        {/* Problems List */}
        <div className="space-y-4">
          {filteredProblems.map((problem) => (
            <div
              key={problem.id}
              className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary hover:border-primary/50 transition-all duration-300"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4 mb-3">
                    {problem.solved ? (
                      <CheckCircle className="h-6 w-6 text-primary" />
                    ) : (
                      <div className="w-6 h-6 border-2 border-gray-400 rounded-full"></div>
                    )}
                    
                    <h3 className="text-xl font-bold text-white hover:text-primary transition-colors cursor-pointer">
                      {problem.title}
                    </h3>
                    
                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getDifficultyBg(problem.difficulty)} ${getDifficultyColor(problem.difficulty)}`}>
                      {problem.difficulty}
                    </span>
                  </div>

                  <p className="text-gray-400 mb-4 line-clamp-2">{problem.description}</p>

                  <div className="flex items-center space-x-6 mb-4">
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span>{problem.timeLimit}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Target className="h-4 w-4" />
                      <span>{problem.acceptanceRate}% acceptance</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Star className="h-4 w-4" />
                      <span>{problem.likes} likes</span>
                    </div>

                    {problem.attempts > 0 && (
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <Code className="h-4 w-4" />
                        <span>{problem.attempts} attempts</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {problem.tags.slice(0, 3).map((tag, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-primary/20 text-primary rounded text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                        {problem.tags.length > 3 && (
                          <span className="text-gray-400 text-xs">+{problem.tags.length - 3} more</span>
                        )}
                      </div>
                    </div>

                    {/* Companies */}
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-400 text-sm">Asked by:</span>
                      <div className="flex space-x-1">
                        {problem.companies.slice(0, 3).map((company, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-dark-tertiary text-gray-300 rounded text-xs"
                          >
                            {company}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="ml-6 flex flex-col space-y-2">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-colors">
                    <Play className="h-4 w-4" />
                    <span>Solve</span>
                  </button>
                  
                  {problem.solved && (
                    <button className="flex items-center space-x-2 px-4 py-2 bg-dark-tertiary hover:bg-dark-tertiary/80 rounded-lg font-semibold transition-colors text-gray-300">
                      <Code className="h-4 w-4" />
                      <span>Review</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProblems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No problems found matching your criteria.</p>
          </div>
        )}

        {/* Daily Challenge */}
        <div className="mt-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl p-8 border border-primary/30">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              🔥 Daily Challenge
            </h2>
            <p className="text-gray-300 mb-6">
              Take on today's featured problem and maintain your solving streak!
            </p>
            <button className="inline-flex items-center space-x-2 px-8 py-3 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-all duration-200 hover:scale-105">
              <Trophy className="h-5 w-5" />
              <span>Accept Challenge</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Practice;
