import React from 'react';
import { Code, Youtube, Github, Twitter, Linkedin, Mail, Heart } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    'Learn': [
      { name: 'Algorithms', href: '#' },
      { name: 'Data Structures', href: '#' },
      { name: 'System Design', href: '#' },
      { name: 'Interview Prep', href: '#' }
    ],
    'Resources': [
      { name: 'YouTube Channel', href: '#' },
      { name: 'Practice Problems', href: '#' },
      { name: 'Code Templates', href: '#' },
      { name: 'Study Guide', href: '#' }
    ],
    'Community': [
      { name: 'Discord Server', href: '#' },
      { name: 'Reddit', href: '#' },
      { name: 'Student Stories', href: '#' },
      { name: 'Office Hours', href: '#' }
    ],
    'Company': [
      { name: 'About Us', href: '#' },
      { name: 'Careers', href: '#' },
      { name: 'Contact', href: '#' },
      { name: 'Privacy Policy', href: '#' }
    ]
  };

  const socialLinks = [
    { icon: Youtube, href: '#', label: 'YouTube' },
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' }
  ];

  return (
    <footer className="bg-dark-secondary border-t border-dark-tertiary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="p-2 bg-primary rounded-lg">
                  <Code className="h-6 w-6 text-white" />
                </div>
                <span className="text-xl font-bold text-white">AlgoStrive</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Empowering developers worldwide to master algorithms and data structures through expert-led tutorials and interactive learning experiences.
              </p>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className="p-2 bg-dark-tertiary rounded-lg hover:bg-primary transition-colors group"
                    aria-label={social.label}
                  >
                    <social.icon className="h-4 w-4 text-gray-400 group-hover:text-white" />
                  </a>
                ))}
              </div>
            </div>

            {/* Links Sections */}
            {Object.entries(footerLinks).map(([category, links]) => (
              <div key={category}>
                <h3 className="text-white font-semibold mb-4">{category}</h3>
                <ul className="space-y-2">
                  {links.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        className="text-gray-400 hover:text-primary transition-colors"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="py-8 border-t border-dark-tertiary">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-semibold text-white mb-2">
                Stay Updated
              </h3>
              <p className="text-gray-400">
                Get notified about new tutorials and courses
              </p>
            </div>
            <div className="flex w-full md:w-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 md:w-64 px-4 py-2 bg-dark-tertiary text-white rounded-l-lg border border-dark-tertiary focus:border-primary focus:outline-none"
              />
              <button className="px-6 py-2 bg-primary hover:bg-primary-light rounded-r-lg font-semibold transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="py-6 border-t border-dark-tertiary">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-1 text-gray-400 mb-4 md:mb-0">
              <span>© {currentYear} AlgoStrive. Made with</span>
              <Heart className="h-4 w-4 text-red-500" />
              <span>for developers worldwide.</span>
            </div>
            
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;