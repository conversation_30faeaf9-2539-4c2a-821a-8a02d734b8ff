import React, { useState } from 'react';
import { 
  Check, 
  X, 
  Star, 
  Crown, 
  Zap,
  Users,
  BookOpen,
  Code,
  Trophy,
  Shield,
  Headphones,
  Download
} from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  period: string;
  description: string;
  features: string[];
  notIncluded?: string[];
  isPopular?: boolean;
  isPremium?: boolean;
  buttonText: string;
  icon: React.ComponentType<any>;
}

const Pricing: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const plans: PricingPlan[] = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      period: 'forever',
      description: 'Perfect for getting started with algorithmic learning',
      icon: BookOpen,
      features: [
        'Access to 20+ free video tutorials',
        'Basic practice problems (50+)',
        'Community forum access',
        'Progress tracking',
        'Mobile app access',
        'Basic code templates'
      ],
      notIncluded: [
        'Premium courses',
        'Live sessions',
        'Personalized learning paths',
        'Interview preparation',
        'Certificate of completion'
      ],
      buttonText: 'Get Started Free'
    },
    {
      id: 'pro',
      name: 'Pro',
      price: billingPeriod === 'monthly' ? 29 : 290,
      originalPrice: billingPeriod === 'yearly' ? 348 : undefined,
      period: billingPeriod === 'monthly' ? 'month' : 'year',
      description: 'Ideal for serious learners and job seekers',
      icon: Star,
      isPopular: true,
      features: [
        'Everything in Free',
        'Access to all premium courses (100+)',
        'Advanced practice problems (500+)',
        'Personalized learning paths',
        'Live coding sessions (weekly)',
        'Interview preparation materials',
        'Code review and feedback',
        'Priority community support',
        'Downloadable resources',
        'Certificate of completion'
      ],
      buttonText: 'Start Pro Trial'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: billingPeriod === 'monthly' ? 99 : 990,
      originalPrice: billingPeriod === 'yearly' ? 1188 : undefined,
      period: billingPeriod === 'monthly' ? 'month' : 'year',
      description: 'For teams and organizations',
      icon: Crown,
      isPremium: true,
      features: [
        'Everything in Pro',
        'Team management dashboard',
        'Custom learning paths',
        'Advanced analytics and reporting',
        'Dedicated account manager',
        '1-on-1 mentoring sessions',
        'Custom integrations',
        'Priority support (24/7)',
        'Bulk user management',
        'Custom branding options',
        'API access',
        'Advanced security features'
      ],
      buttonText: 'Contact Sales'
    }
  ];

  const faqs = [
    {
      question: 'Can I switch plans anytime?',
      answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.'
    },
    {
      question: 'Is there a free trial for paid plans?',
      answer: 'Yes, we offer a 7-day free trial for the Pro plan. No credit card required to start.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards, PayPal, and bank transfers for enterprise customers.'
    },
    {
      question: 'Do you offer student discounts?',
      answer: 'Yes, we offer 50% discount for students with valid student ID. Contact support for details.'
    },
    {
      question: 'Can I cancel my subscription anytime?',
      answer: 'Absolutely. You can cancel your subscription at any time from your account settings.'
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Software Engineer at Google',
      content: 'AlgoStrive Pro helped me land my dream job at Google. The interview preparation materials are top-notch!',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      name: 'Mike Johnson',
      role: 'Senior Developer at Microsoft',
      content: 'The personalized learning paths saved me months of preparation time. Highly recommended!',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      name: 'Emily Rodriguez',
      role: 'Tech Lead at Amazon',
      content: 'Best investment I made for my career. The live sessions and mentoring are invaluable.',
      avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ];

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Choose Your <span className="text-primary">Learning Path</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Unlock your potential with our comprehensive learning platform. Start free and upgrade as you grow.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <span className={`font-medium ${billingPeriod === 'monthly' ? 'text-white' : 'text-gray-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingPeriod === 'yearly' ? 'bg-primary' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`font-medium ${billingPeriod === 'yearly' ? 'text-white' : 'text-gray-400'}`}>
              Yearly
            </span>
            {billingPeriod === 'yearly' && (
              <span className="bg-primary/20 text-primary px-3 py-1 rounded-full text-sm font-medium">
                Save 17%
              </span>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-dark-secondary rounded-2xl border transition-all duration-300 hover:-translate-y-2 ${
                plan.isPopular
                  ? 'border-primary shadow-2xl shadow-primary/20 scale-105'
                  : plan.isPremium
                  ? 'border-accent shadow-2xl shadow-accent/20'
                  : 'border-dark-tertiary hover:border-primary/50'
              }`}
            >
              {/* Popular Badge */}
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-primary text-white px-6 py-2 rounded-full text-sm font-bold">
                    Most Popular
                  </div>
                </div>
              )}

              {/* Premium Badge */}
              {plan.isPremium && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-accent text-dark px-6 py-2 rounded-full text-sm font-bold">
                    Enterprise
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    plan.isPopular
                      ? 'bg-primary/20'
                      : plan.isPremium
                      ? 'bg-accent/20'
                      : 'bg-gray-600/20'
                  }`}>
                    <plan.icon className={`h-8 w-8 ${
                      plan.isPopular
                        ? 'text-primary'
                        : plan.isPremium
                        ? 'text-accent'
                        : 'text-gray-400'
                    }`} />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-gray-400 mb-4">{plan.description}</p>
                  
                  <div className="mb-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-white">${plan.price}</span>
                      <span className="text-gray-400 ml-2">/{plan.period}</span>
                    </div>
                    {plan.originalPrice && (
                      <div className="text-gray-500 line-through text-lg">
                        ${plan.originalPrice}/{plan.period}
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-8">
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <Check className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                    {plan.notIncluded?.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <X className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-500">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* CTA Button */}
                <button
                  className={`w-full py-4 px-6 rounded-lg font-semibold transition-all duration-200 hover:scale-105 ${
                    plan.isPopular
                      ? 'bg-primary hover:bg-primary-light text-white'
                      : plan.isPremium
                      ? 'bg-accent hover:bg-accent/80 text-dark'
                      : 'bg-dark-tertiary hover:bg-primary text-gray-300 hover:text-white'
                  }`}
                >
                  {plan.buttonText}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="bg-dark-secondary rounded-2xl p-8 border border-dark-tertiary mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Compare All Features
          </h2>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-dark-tertiary">
                  <th className="text-left py-4 text-white font-semibold">Features</th>
                  <th className="text-center py-4 text-white font-semibold">Free</th>
                  <th className="text-center py-4 text-white font-semibold">Pro</th>
                  <th className="text-center py-4 text-white font-semibold">Enterprise</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {[
                  { feature: 'Video Tutorials', free: '20+', pro: '100+', enterprise: 'Unlimited' },
                  { feature: 'Practice Problems', free: '50+', pro: '500+', enterprise: '1000+' },
                  { feature: 'Live Sessions', free: false, pro: true, enterprise: true },
                  { feature: 'Personalized Paths', free: false, pro: true, enterprise: true },
                  { feature: 'Interview Prep', free: false, pro: true, enterprise: true },
                  { feature: 'Team Management', free: false, pro: false, enterprise: true },
                  { feature: '1-on-1 Mentoring', free: false, pro: false, enterprise: true }
                ].map((row, index) => (
                  <tr key={index} className="border-b border-dark-tertiary/50">
                    <td className="py-4 font-medium">{row.feature}</td>
                    <td className="py-4 text-center">
                      {typeof row.free === 'boolean' ? (
                        row.free ? <Check className="h-5 w-5 text-primary mx-auto" /> : <X className="h-5 w-5 text-gray-500 mx-auto" />
                      ) : (
                        row.free
                      )}
                    </td>
                    <td className="py-4 text-center">
                      {typeof row.pro === 'boolean' ? (
                        row.pro ? <Check className="h-5 w-5 text-primary mx-auto" /> : <X className="h-5 w-5 text-gray-500 mx-auto" />
                      ) : (
                        row.pro
                      )}
                    </td>
                    <td className="py-4 text-center">
                      {typeof row.enterprise === 'boolean' ? (
                        row.enterprise ? <Check className="h-5 w-5 text-primary mx-auto" /> : <X className="h-5 w-5 text-gray-500 mx-auto" />
                      ) : (
                        row.enterprise
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            What Our Students Say
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-dark-secondary rounded-xl p-6 border border-dark-tertiary">
                <div className="flex items-center space-x-4 mb-4">
                  <img 
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="text-white font-semibold">{testimonial.name}</h4>
                    <p className="text-gray-400 text-sm">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-300 italic">"{testimonial.content}"</p>
                <div className="flex mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ */}
        <div className="bg-dark-secondary rounded-2xl p-8 border border-dark-tertiary">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Frequently Asked Questions
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {faqs.map((faq, index) => (
              <div key={index}>
                <h3 className="text-white font-semibold mb-2">{faq.question}</h3>
                <p className="text-gray-400">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
