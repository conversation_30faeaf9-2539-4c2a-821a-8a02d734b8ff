import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  MapPin, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Trophy,
  Target,
  CheckCircle,
  ArrowRight,
  Play,
  Award
} from 'lucide-react';

interface Course {
  id: string;
  title: string;
  duration: string;
  isCompleted: boolean;
  isLocked: boolean;
}

interface LearningPath {
  id: string;
  title: string;
  description: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: string;
  students: number;
  rating: number;
  courses: Course[];
  skills: string[];
  thumbnail: string;
  progress: number;
  isEnrolled: boolean;
  completedCourses: number;
  totalCourses: number;
}

const LearningPaths: React.FC = () => {
  const [selectedLevel, setSelectedLevel] = useState('All');

  const learningPaths: LearningPath[] = [
    {
      id: '1',
      title: 'Complete Software Engineer Path',
      description: 'Master everything from basic programming to advanced system design. Perfect for aspiring software engineers.',
      level: 'Beginner',
      duration: '6 months',
      students: 15420,
      rating: 4.9,
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400',
      skills: ['Programming Fundamentals', 'Data Structures', 'Algorithms', 'System Design', 'Databases'],
      progress: 65,
      isEnrolled: true,
      completedCourses: 4,
      totalCourses: 8,
      courses: [
        { id: '1-1', title: 'Programming Fundamentals', duration: '3 weeks', isCompleted: true, isLocked: false },
        { id: '1-2', title: 'Data Structures Basics', duration: '4 weeks', isCompleted: true, isLocked: false },
        { id: '1-3', title: 'Algorithm Design', duration: '5 weeks', isCompleted: true, isLocked: false },
        { id: '1-4', title: 'Advanced Data Structures', duration: '4 weeks', isCompleted: true, isLocked: false },
        { id: '1-5', title: 'Dynamic Programming', duration: '3 weeks', isCompleted: false, isLocked: false },
        { id: '1-6', title: 'Graph Algorithms', duration: '4 weeks', isCompleted: false, isLocked: false },
        { id: '1-7', title: 'System Design Basics', duration: '5 weeks', isCompleted: false, isLocked: true },
        { id: '1-8', title: 'Advanced System Design', duration: '6 weeks', isCompleted: false, isLocked: true }
      ]
    },
    {
      id: '2',
      title: 'FAANG Interview Preparation',
      description: 'Intensive preparation for technical interviews at top tech companies like Google, Facebook, Amazon.',
      level: 'Advanced',
      duration: '4 months',
      students: 8934,
      rating: 4.8,
      thumbnail: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=400',
      skills: ['Coding Interviews', 'System Design', 'Behavioral Questions', 'Mock Interviews'],
      progress: 0,
      isEnrolled: false,
      completedCourses: 0,
      totalCourses: 6,
      courses: [
        { id: '2-1', title: 'Interview Fundamentals', duration: '2 weeks', isCompleted: false, isLocked: false },
        { id: '2-2', title: 'Advanced Algorithms', duration: '6 weeks', isCompleted: false, isLocked: false },
        { id: '2-3', title: 'System Design Interviews', duration: '4 weeks', isCompleted: false, isLocked: true },
        { id: '2-4', title: 'Behavioral Interviews', duration: '2 weeks', isCompleted: false, isLocked: true },
        { id: '2-5', title: 'Mock Interview Practice', duration: '4 weeks', isCompleted: false, isLocked: true },
        { id: '2-6', title: 'Salary Negotiation', duration: '1 week', isCompleted: false, isLocked: true }
      ]
    },
    {
      id: '3',
      title: 'Competitive Programming Mastery',
      description: 'Excel in competitive programming contests and algorithmic challenges.',
      level: 'Intermediate',
      duration: '5 months',
      students: 3247,
      rating: 4.7,
      thumbnail: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400',
      skills: ['Contest Strategies', 'Advanced Algorithms', 'Mathematical Concepts', 'Optimization'],
      progress: 25,
      isEnrolled: true,
      completedCourses: 1,
      totalCourses: 5,
      courses: [
        { id: '3-1', title: 'Contest Fundamentals', duration: '3 weeks', isCompleted: true, isLocked: false },
        { id: '3-2', title: 'Advanced Graph Theory', duration: '5 weeks', isCompleted: false, isLocked: false },
        { id: '3-3', title: 'Number Theory & Math', duration: '4 weeks', isCompleted: false, isLocked: false },
        { id: '3-4', title: 'String Algorithms', duration: '4 weeks', isCompleted: false, isLocked: true },
        { id: '3-5', title: 'Contest Strategies', duration: '3 weeks', isCompleted: false, isLocked: true }
      ]
    },
    {
      id: '4',
      title: 'Data Science & Algorithms',
      description: 'Learn algorithms specifically for data science and machine learning applications.',
      level: 'Intermediate',
      duration: '4 months',
      students: 5678,
      rating: 4.6,
      thumbnail: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=400',
      skills: ['Data Analysis', 'Machine Learning', 'Statistics', 'Python'],
      progress: 0,
      isEnrolled: false,
      completedCourses: 0,
      totalCourses: 6,
      courses: [
        { id: '4-1', title: 'Data Structures for DS', duration: '3 weeks', isCompleted: false, isLocked: false },
        { id: '4-2', title: 'Statistical Algorithms', duration: '4 weeks', isCompleted: false, isLocked: false },
        { id: '4-3', title: 'ML Algorithms', duration: '5 weeks', isCompleted: false, isLocked: true },
        { id: '4-4', title: 'Optimization Methods', duration: '3 weeks', isCompleted: false, isLocked: true },
        { id: '4-5', title: 'Big Data Algorithms', duration: '4 weeks', isCompleted: false, isLocked: true },
        { id: '4-6', title: 'Real-world Projects', duration: '5 weeks', isCompleted: false, isLocked: true }
      ]
    }
  ];

  const levels = ['All', 'Beginner', 'Intermediate', 'Advanced'];

  const filteredPaths = learningPaths.filter(path => 
    selectedLevel === 'All' || path.level === selectedLevel
  );

  return (
    <div className="min-h-screen bg-dark pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Learning <span className="text-primary">Paths</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Structured learning journeys designed to take you from beginner to expert in specific domains
          </p>
        </div>

        {/* Filter */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-dark-secondary rounded-lg p-1 border border-dark-tertiary">
            {levels.map((level) => (
              <button
                key={level}
                onClick={() => setSelectedLevel(level)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  selectedLevel === level
                    ? 'bg-primary text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                {level}
              </button>
            ))}
          </div>
        </div>

        {/* Learning Paths Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredPaths.map((path) => (
            <div
              key={path.id}
              className="bg-dark-secondary rounded-2xl border border-dark-tertiary overflow-hidden hover:border-primary/50 transition-all duration-300 hover:-translate-y-1"
            >
              {/* Header */}
              <div className="relative">
                <img
                  src={path.thumbnail}
                  alt={path.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4 bg-black/70 px-3 py-1 rounded-full text-sm font-medium">
                  {path.level}
                </div>
                {path.isEnrolled && (
                  <div className="absolute top-4 right-4 bg-primary px-3 py-1 rounded-full text-sm font-medium">
                    Enrolled
                  </div>
                )}
              </div>

              <div className="p-6">
                {/* Title and Description */}
                <h3 className="text-2xl font-bold text-white mb-3">{path.title}</h3>
                <p className="text-gray-400 mb-4">{path.description}</p>

                {/* Stats */}
                <div className="flex items-center space-x-6 mb-4 text-sm text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{path.duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{path.totalCourses} courses</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{path.students.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{path.rating}</span>
                  </div>
                </div>

                {/* Progress (if enrolled) */}
                {path.isEnrolled && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
                      <span>Progress</span>
                      <span>{path.completedCourses}/{path.totalCourses} courses completed</span>
                    </div>
                    <div className="w-full bg-dark-tertiary rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${path.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Skills */}
                <div className="mb-6">
                  <h4 className="text-white font-semibold mb-2">Skills you'll gain:</h4>
                  <div className="flex flex-wrap gap-2">
                    {path.skills.map((skill, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Course List Preview */}
                <div className="mb-6">
                  <h4 className="text-white font-semibold mb-3">Course Roadmap:</h4>
                  <div className="space-y-2">
                    {path.courses.slice(0, 3).map((course, index) => (
                      <div key={course.id} className="flex items-center space-x-3 p-2 bg-dark-tertiary rounded-lg">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/20 text-primary text-xs font-bold">
                          {index + 1}
                        </div>
                        {course.isCompleted ? (
                          <CheckCircle className="h-4 w-4 text-primary" />
                        ) : course.isLocked ? (
                          <div className="w-4 h-4 border border-gray-500 rounded-full"></div>
                        ) : (
                          <div className="w-4 h-4 border-2 border-primary rounded-full"></div>
                        )}
                        <span className={`flex-1 text-sm ${course.isLocked ? 'text-gray-500' : 'text-white'}`}>
                          {course.title}
                        </span>
                        <span className="text-xs text-gray-400">{course.duration}</span>
                      </div>
                    ))}
                    {path.courses.length > 3 && (
                      <div className="text-center text-gray-400 text-sm">
                        +{path.courses.length - 3} more courses
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex space-x-3">
                  {path.isEnrolled ? (
                    <>
                      <Link
                        to={`/learning-paths/${path.id}`}
                        className="flex-1 inline-flex items-center justify-center space-x-2 py-3 px-4 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-all duration-200 hover:scale-105"
                      >
                        <Play className="h-4 w-4" />
                        <span>Continue Path</span>
                      </Link>
                      <button className="px-4 py-3 bg-dark-tertiary hover:bg-dark-tertiary/80 rounded-lg transition-colors">
                        <Trophy className="h-4 w-4 text-primary" />
                      </button>
                    </>
                  ) : (
                    <Link
                      to={`/learning-paths/${path.id}`}
                      className="flex-1 inline-flex items-center justify-center space-x-2 py-3 px-4 bg-dark-tertiary hover:bg-primary text-gray-300 hover:text-white rounded-lg font-semibold transition-all duration-200 hover:scale-105"
                    >
                      <span>Start Learning Path</span>
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="mt-16 bg-dark-secondary rounded-2xl p-8 border border-dark-tertiary">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Why Choose Learning Paths?
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Our structured learning paths provide a clear roadmap to mastery, ensuring you build skills progressively and efficiently.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Structured Learning</h3>
              <p className="text-gray-400">
                Follow a carefully designed curriculum that builds knowledge step by step
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Goal-Oriented</h3>
              <p className="text-gray-400">
                Each path is designed with specific career goals and outcomes in mind
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Certification</h3>
              <p className="text-gray-400">
                Earn certificates upon completion to showcase your achievements
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearningPaths;
