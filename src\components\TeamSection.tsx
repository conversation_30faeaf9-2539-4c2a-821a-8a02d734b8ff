import React from 'react';
import { Gith<PERSON>, Linkedin, Twitter, Mail, Award, Users, BookOpen } from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  image: string;
  expertise: string[];
  social: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    email?: string;
  };
  achievements: string[];
}

const TeamSection: React.FC = () => {
  const teamMembers: TeamMember[] = [
    {
      id: '1',
      name: '<PERSON>',
      role: 'Founder & Lead Instructor',
      bio: 'Former Google SWE with 8+ years in competitive programming. Passionate about making complex algorithms accessible to everyone.',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
      expertise: ['Algorithms', 'System Design', 'Competitive Programming'],
      social: {
        github: 'https://github.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
        email: '<EMAIL>'
      },
      achievements: ['ICPC World Finalist', 'Google Code Jam Winner', '10M+ YouTube Views']
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Senior Algorithm Instructor',
      bio: 'PhD in Computer Science from MIT. Expert in dynamic programming and graph algorithms with a talent for clear explanations.',
      image: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
      expertise: ['Dynamic Programming', 'Graph Theory', 'Mathematical Algorithms'],
      social: {
        github: 'https://github.com',
        linkedin: 'https://linkedin.com',
        email: '<EMAIL>'
      },
      achievements: ['MIT PhD', 'Published 15+ Research Papers', 'ACM Distinguished Scientist']
    },
    {
      id: '3',
      name: 'David Kim',
      role: 'Data Structures Specialist',
      bio: 'Former Microsoft engineer specializing in advanced data structures and their real-world applications in large-scale systems.',
      image: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
      expertise: ['Data Structures', 'System Architecture', 'Performance Optimization'],
      social: {
        github: 'https://github.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com'
      },
      achievements: ['Microsoft Senior SWE', 'Performance Optimization Expert', '5M+ Students Taught']
    },
    {
      id: '4',
      name: 'Emily Watson',
      role: 'Interview Prep Coach',
      bio: 'Former FAANG interviewer with expertise in technical interview preparation and career guidance for software engineers.',
      image: 'https://images.pexels.com/photos/1181695/pexels-photo-1181695.jpeg?auto=compress&cs=tinysrgb&w=400',
      expertise: ['Interview Preparation', 'System Design', 'Career Coaching'],
      social: {
        linkedin: 'https://linkedin.com',
        email: '<EMAIL>'
      },
      achievements: ['FAANG Interviewer', 'Career Coach', '95% Interview Success Rate']
    }
  ];

  const stats = [
    { icon: Users, label: 'Students Taught', value: '50,000+' },
    { icon: BookOpen, label: 'Courses Created', value: '200+' },
    { icon: Award, label: 'Success Rate', value: '94%' }
  ];

  return (
    <section id="team" className="py-20 bg-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Meet Our <span className="text-primary">Expert Team</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Learn from industry veterans who have worked at top tech companies and have extensive teaching experience
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/20 rounded-full mb-4 group-hover:bg-primary/30 transition-colors">
                <stat.icon className="h-8 w-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
              <div className="text-gray-400">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member) => (
            <div
              key={member.id}
              className="group bg-dark-secondary rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-primary/20 transition-all duration-300 hover:-translate-y-2"
            >
              {/* Image */}
              <div className="relative overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-secondary/80 to-transparent"></div>
                
                {/* Social Links */}
                <div className="absolute bottom-4 left-4 right-4 flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {member.social.github && (
                    <a
                      href={member.social.github}
                      className="p-2 bg-dark/80 rounded-full hover:bg-primary transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Github className="h-4 w-4 text-white" />
                    </a>
                  )}
                  {member.social.linkedin && (
                    <a
                      href={member.social.linkedin}
                      className="p-2 bg-dark/80 rounded-full hover:bg-primary transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Linkedin className="h-4 w-4 text-white" />
                    </a>
                  )}
                  {member.social.twitter && (
                    <a
                      href={member.social.twitter}
                      className="p-2 bg-dark/80 rounded-full hover:bg-primary transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Twitter className="h-4 w-4 text-white" />
                    </a>
                  )}
                  {member.social.email && (
                    <a
                      href={`mailto:${member.social.email}`}
                      className="p-2 bg-dark/80 rounded-full hover:bg-primary transition-colors"
                    >
                      <Mail className="h-4 w-4 text-white" />
                    </a>
                  )}
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-1">{member.name}</h3>
                <p className="text-primary mb-3 font-medium">{member.role}</p>
                <p className="text-gray-400 text-sm mb-4 line-clamp-3">{member.bio}</p>

                {/* Expertise Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {member.expertise.map((skill, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-dark-tertiary rounded-full text-xs text-gray-300"
                    >
                      {skill}
                    </span>
                  ))}
                </div>

                {/* Achievements */}
                <div className="space-y-1">
                  {member.achievements.slice(0, 2).map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-2 text-xs text-gray-500">
                      <Award className="h-3 w-3 text-primary flex-shrink-0" />
                      <span>{achievement}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Join Team CTA */}
        <div className="mt-16 text-center">
          <div className="bg-dark-secondary rounded-2xl p-8 max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Want to Join Our Team?
            </h3>
            <p className="text-gray-400 mb-6">
              We're always looking for passionate educators and industry experts to help us create amazing content
            </p>
            <button className="inline-flex items-center space-x-2 px-6 py-3 bg-primary hover:bg-primary-light rounded-lg font-semibold transition-all duration-200 hover:scale-105">
              <Mail className="h-4 w-4" />
              <span>Get in Touch</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TeamSection;