import { StackClientApp } from "@stackframe/react";
import { useNavigate } from "react-router-dom";

export const stackClientApp = new StackClientApp({
  // You should store these in environment variables
  projectId: "61fff9c5-14c7-46ed-b033-0261b844361d",
  publishableClientKey: "pck_1g4xvy377gb0htj3mqwrdrf05h4zhhnb29fxgemx21b70",
  tokenStore: "cookie",
  redirectMethod: {
    useNavigate,
  }
});
